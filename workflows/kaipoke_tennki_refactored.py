"""
Kaipoke Tennki Workflow (修复版 - 智能浏览器管理 + 数据保护)
修复问题：
1. 浏览器实例管理问题：按数据量动态启动浏览器
2. 数据登录流程中的字段清空问题：增强数据保护机制
3. 数据分割策略：按利用者分割，确保测试数据正确分配
4. 失败数据日志：增强详细输出，便于手动登录

修复日期：2025-01-27
修复内容：
- 智能浏览器数量计算
- 数据保护机制增强
- 弹窗处理优化
- 表单状态管理改进
- 按利用者分割数据
- 增强失败数据日志输出
"""

import asyncio
import os
import sys
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, Browser, Page

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logger_config import logger
from core.selector_executor import SelectorExecutor
from core.rpa_tools.kaipoke_login_service import kaipoke_login_with_env
from core.gsuite.sheets_client import SheetsClient
from core.rpa_tools.data_processor import DataProcessor
from core.rpa_tools.tennki_data_processor import TennkiDataProcessor
from core.rpa_tools.tennki_form_engine import TennkiFormEngine, TennkiFailedDataCollector
from core.rpa_tools.tennki_facility_manager import TennkiFacilityManager
from core.rpa_tools.tennki_performance_monitor import TennkiPerformanceMonitor
from core.popup_handler.popup_guardian import start_popup_guardian, stop_popup_guardian, stop_all_popup_guardians


class SmartBrowserManager:
    """🆕 智能浏览器管理器 - 根据数据量动态分配浏览器实例"""

    def __init__(self):
        self.playwright = None
        self.browsers = {}  # facility_name -> Browser
        self.pages = {}     # facility_name -> Page
        self.popup_guardians = {}  # facility_name -> guardian_status
        # 🆕 调整数据量阈值，适应测试环境
        self.data_volume_threshold = {
            'small': 50,     # 小数据量：1个浏览器 (适合测试环境)
            'medium': 150,   # 中等数据量：2个浏览器
            'large': 300     # 大数据量：3个浏览器
        }
        # 🆕 登录互斥机制
        self.login_lock = asyncio.Lock()
        self.login_queue = asyncio.Queue()
        self.login_status = {}  # facility_name -> login_status

    async def initialize(self):
        """初始化Playwright"""
        if self.playwright is None:
            self.playwright = await async_playwright().start()
            logger.info("🚀 SmartBrowserManager初始化完成")

    def calculate_optimal_browser_count(self, total_records: int, total_users: int) -> int:
        """🆕 智能计算最优浏览器数量（按利用者分割）"""
        logger.info(f"📊 计算最优浏览器数量: {total_records}条记录, {total_users}个用户")

        # 🆕 优先按用户数量计算（按利用者分割）
        if total_users <= 1:
            optimal_count = 1
            reason = f"只有{total_users}个用户，使用1个浏览器"
        elif total_users <= 2:
            optimal_count = 2
            reason = f"有{total_users}个用户，按利用者分割使用2个浏览器"
        elif total_users <= 3:
            optimal_count = 3
            reason = f"有{total_users}个用户，按利用者分割使用3个浏览器"
        elif total_users <= 4:
            optimal_count = 4
            reason = f"有{total_users}个用户，按利用者分割使用4个浏览器"
        else:
            # 大数据量情况
            if total_records <= self.data_volume_threshold['small']:
                optimal_count = 1
                reason = f"小数据量({total_records}条) <= {self.data_volume_threshold['small']}"
            elif total_records <= self.data_volume_threshold['medium']:
                optimal_count = 2
                reason = f"中等数据量({total_records}条) <= {self.data_volume_threshold['medium']}"
            elif total_records <= self.data_volume_threshold['large']:
                optimal_count = 3
                reason = f"大数据量({total_records}条) <= {self.data_volume_threshold['large']}"
            else:
                optimal_count = 4  # 最大4个浏览器
                reason = f"超大数据量({total_records}条) > {self.data_volume_threshold['large']}"

        # 🆕 用户数量限制：确保每个浏览器至少有1个用户处理
        min_users_per_browser = 1  # 测试环境可以每个浏览器1个用户
        max_browsers_by_users = max(1, total_users // min_users_per_browser)

        # 取较小值，避免浏览器过多
        final_count = min(optimal_count, max_browsers_by_users)

        logger.info(f"✅ 最优浏览器数量: {final_count} ({reason})")
        logger.info(f"   - 按数据量计算: {optimal_count}")
        logger.info(f"   - 按用户数限制: {max_browsers_by_users}")
        logger.info(f"   - 最终决定: {final_count}")

        return final_count

    async def create_browser_for_facility(self, facility_name: str, headless: bool = False) -> tuple[Browser, Page]:
        """为指定据点创建独立的浏览器实例（增强版 - 序列化登录）"""
        try:
            if facility_name in self.browsers:
                logger.info(f"🔄 据点 {facility_name} 的浏览器已存在，复用中...")
                return self.browsers[facility_name], self.pages[facility_name]

            # 🆕 使用登录互斥锁，确保序列化登录
            async with self.login_lock:
                logger.info(f"🔐 据点 {facility_name} 获得登录锁，开始创建浏览器...")

                # 再次检查（双重检查锁定模式）
                if facility_name in self.browsers:
                    logger.info(f"🔄 据点 {facility_name} 的浏览器在等待期间已创建，复用中...")
                    return self.browsers[facility_name], self.pages[facility_name]

                logger.info(f"🌐 为据点 {facility_name} 创建新的浏览器实例...")

                # 创建新的浏览器实例
                browser = await self.playwright.firefox.launch(headless=headless)
                page = await browser.new_page()

                # 🆕 增强页面保护机制
                await self._setup_enhanced_page_protection(page, facility_name)

                # 存储实例
                self.browsers[facility_name] = browser
                self.pages[facility_name] = page
                self.login_status[facility_name] = 'created'

                # 启动弹窗守护（使用增强配置）
                page_id = f"tennki_{facility_name}"
                guardian_started = await start_popup_guardian(page, page_id, "tennki_form_protected")

                self.popup_guardians[facility_name] = guardian_started

                if guardian_started:
                    logger.info(f"🛡️ 据点 {facility_name} 增强弹窗守护启动成功")
                else:
                    logger.warning(f"⚠️ 据点 {facility_name} 弹窗守护启动失败")

                # 🆕 等待一段时间确保浏览器完全初始化
                await asyncio.sleep(2)

                logger.info(f"✅ 据点 {facility_name} 的浏览器实例创建成功")
                logger.info(f"🔓 据点 {facility_name} 释放登录锁")

                return browser, page

        except Exception as e:
            logger.error(f"❌ 为据点 {facility_name} 创建浏览器失败: {e}")
            # 清理失败状态
            if facility_name in self.login_status:
                del self.login_status[facility_name]
            raise

    async def _setup_enhanced_page_protection(self, page: Page, facility_name: str):
        """🆕 设置增强页面保护机制"""
        try:
            logger.debug(f"🛡️ 为据点 {facility_name} 设置增强页面保护...")

            # 注入增强保护脚本
            await page.evaluate("""
                () => {
                    // 🆕 数据保护全局标志
                    window.TENNKI_DATA_PROTECTION = {
                        enabled: true,
                        protectedFields: new Set(),
                        originalValues: new Map(),
                        facility: arguments[0]
                    };

                    // 🆕 保护表单数据不被意外清空
                    const protectFormData = () => {
                        const formFields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                        formFields.forEach(field => {
                            if (field.value && field.value.trim() !== '') {
                                window.TENNKI_DATA_PROTECTION.protectedFields.add(field.id || field.name);
                                window.TENNKI_DATA_PROTECTION.originalValues.set(field.id || field.name, field.value);
                            }
                        });
                    };

                    // 🆕 监控字段值变化，防止意外清空
                    const monitorFieldChanges = () => {
                        const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                                    const field = mutation.target;
                                    const fieldId = field.id || field.name;
                                    
                                    if (window.TENNKI_DATA_PROTECTION.protectedFields.has(fieldId)) {
                                        const originalValue = window.TENNKI_DATA_PROTECTION.originalValues.get(fieldId);
                                        if (field.value === '' && originalValue) {
                                            console.warn('🛡️ 检测到字段被意外清空，恢复原值:', fieldId);
                                            field.value = originalValue;
                                        }
                                    }
                                }
                            });
                        });

                        const formContainer = document.querySelector('#registModal');
                        if (formContainer) {
                            observer.observe(formContainer, {
                                attributes: true,
                                subtree: true,
                                attributeFilter: ['value']
                            });
                        }
                    };

                    // 🆕 增强弹窗识别，避免误关闭数据表单
                    const enhancedPopupDetection = () => {
                        // 重写常见的弹窗关闭函数
                        const originalClose = window.close;
                        window.close = function() {
                            const registModal = document.querySelector('#registModal');
                            if (registModal && registModal.style.display !== 'none') {
                                console.log('🛡️ 阻止关闭窗口，保护数据登录表单');
                                return false;
                            }
                            return originalClose.apply(this, arguments);
                        };

                        // 监控模态框状态变化
                        const modalObserver = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                                    const modal = mutation.target;
                                    if (modal.id === 'registModal' && modal.style.display === 'none') {
                                        console.log('🛡️ 检测到数据表单被关闭，记录保护状态');
                                    }
                                }
                            });
                        });

                        const registModal = document.querySelector('#registModal');
                        if (registModal) {
                            modalObserver.observe(registModal, {
                                attributes: true,
                                attributeFilter: ['style']
                            });
                        }
                    };

                    // 初始化保护机制
                    protectFormData();
                    monitorFieldChanges();
                    enhancedPopupDetection();

                    console.log('🛡️ 增强页面保护机制已激活');
                }
            """, facility_name)

            logger.debug(f"✅ 据点 {facility_name} 增强页面保护设置完成")

        except Exception as e:
            logger.warning(f"⚠️ 设置据点 {facility_name} 页面保护失败: {e}")

    async def close_facility_browser(self, facility_name: str):
        """关闭指定据点的浏览器"""
        try:
            if facility_name in self.browsers:
                # 停止弹窗守护
                if facility_name in self.popup_guardians:
                    await stop_popup_guardian(facility_name)
                    del self.popup_guardians[facility_name]

                # 关闭浏览器
                await self.browsers[facility_name].close()
                del self.browsers[facility_name]
                del self.pages[facility_name]
                logger.info(f"✅ 据点 {facility_name} 的浏览器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭据点 {facility_name} 的浏览器失败: {e}")

    async def close_all_browsers(self):
        """关闭所有浏览器"""
        try:
            for facility_name in list(self.browsers.keys()):
                await self.close_facility_browser(facility_name)
            logger.info("✅ 所有浏览器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭所有浏览器失败: {e}")

    def get_active_browser_count(self) -> int:
        """获取活跃浏览器数量"""
        return len(self.browsers)


class EnhancedTennkiFacilityProcessor:
    """🆕 增强据点处理器 - 智能浏览器管理 + 按利用者分割"""

    def __init__(self, facility_config: dict, workflow_config: dict, smart_browser_manager: SmartBrowserManager):
        self.facility_config = facility_config
        self.workflow_config = workflow_config
        self.smart_browser_manager = smart_browser_manager
        self.facility_name = facility_config.get('name', 'unknown')
        self.facility_manager = None
        self.form_engine = None
        self.performance_monitor = None
        self.failed_data_collector = None

    async def initialize(self):
        """初始化处理器（不启动浏览器）"""
        try:
            logger.info(f"🏥 初始化据点处理器: {self.facility_name}")

            # 初始化智能浏览器管理器
            await self.smart_browser_manager.initialize()

            # 初始化性能监控器
            self.performance_monitor = TennkiPerformanceMonitor()

            # 初始化失败数据收集器
            self.failed_data_collector = TennkiFailedDataCollector()

            logger.info(f"✅ 据点处理器初始化完成: {self.facility_name}")

        except Exception as e:
            logger.error(f"❌ 据点处理器初始化失败: {e}")
            raise

    async def initialize_browser_and_login(self):
        """🆕 初始化浏览器并登录（在数据处理后调用）"""
        try:
            logger.info(f"🌐 为据点 {self.facility_name} 初始化浏览器并登录")

            # 创建浏览器实例
            browser, page = await self.smart_browser_manager.create_browser_for_facility(
                self.facility_name,
                headless=self.workflow_config.get('headless', False)
            )

            selector_executor = SelectorExecutor(page)

            # 初始化据点管理器
            self.facility_manager = TennkiFacilityManager(selector_executor)

            self.form_engine = EnhancedTennkiFormEngine(
                selector_executor,
                self.performance_monitor,
                self.failed_data_collector
            )

            # 登录系统
            await kaipoke_login_with_env(
                page,
                self.workflow_config.get('corporation_id_env', 'KAIPOKE_CORPORATION_ID'),
                self.workflow_config.get('login_id_env', 'KAIPOKE_MEMBER_LOGIN_ID'),
                self.workflow_config.get('password_env', 'KAIPOKE_PASSWORD'),
                self.workflow_config.get('login_url')
            )

            # 🆕 修复：添加缺失的主菜单点击步骤（参考RPA代码）
            await self._navigate_to_receipt_menu(page)

            logger.info(f"✅ 据点 {self.facility_name} 浏览器初始化并登录完成")

        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 浏览器初始化失败: {e}")
            raise

    async def _navigate_to_receipt_menu(self, page):
        """🆕 导航到レセプト菜单（参考RPA代码实现）"""
        try:
            logger.info("📋 点击レセプト主菜单...")

            # 等待页面完全加载
            await page.wait_for_load_state('networkidle', timeout=30000)

            # 🆕 多重策略点击主菜单（参考RPA代码中的 .mainCtg li:nth-of-type(1) a）
            main_menu_clicked = False

            # 策略1：使用RPA代码中的精确选择器
            try:
                await page.wait_for_selector('.mainCtg li:nth-of-type(1) a', timeout=10000)
                await page.click('.mainCtg li:nth-of-type(1) a')
                main_menu_clicked = True
                logger.info("✅ 使用RPA精确选择器成功点击主菜单")
            except Exception as e:
                logger.debug(f"⚠️ RPA精确选择器失败: {e}")

            # 策略2：使用智能选择器
            if not main_menu_clicked:
                try:
                    success = await self.facility_manager.selector_executor.smart_click(
                        workflow="kaipoke_tennki",
                        category="navigation",
                        element="main_menu",
                        target_text="レセプト"
                    )
                    if success:
                        main_menu_clicked = True
                        logger.info("✅ 智能选择器成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 智能选择器失败: {e}")

            # 策略3：文本匹配
            if not main_menu_clicked:
                try:
                    await page.wait_for_selector("text=レセプト", timeout=10000)
                    await page.click("text=レセプト")
                    main_menu_clicked = True
                    logger.info("✅ 文本匹配成功点击主菜单")
                except Exception as e:
                    logger.debug(f"⚠️ 文本匹配失败: {e}")

            if not main_menu_clicked:
                raise Exception("所有策略都无法点击レセプト主菜单")

            # 等待页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            logger.info("✅ レセプト主菜单点击完成，页面已加载")

        except Exception as e:
            logger.error(f"❌ 导航到レセプト菜单失败: {e}")
            raise

    async def process_facility_data(self):
        """处理据点数据（智能并行处理）"""
        try:
            logger.info(f"🔄 开始处理据点数据: {self.facility_name}")

            # 🆕 第一步：获取和处理数据（不启动浏览器）
            processed_data = await self._get_and_process_data()

            if not processed_data:
                logger.warning(f"⚠️ 据点 {self.facility_name} 没有数据需要处理")
                return

            logger.info(f"📊 据点 {self.facility_name} 共有 {len(processed_data)} 个用户的数据需要处理")

            # 🆕 第二步：计算最优浏览器数量
            total_records = sum(user['total_records'] for user in processed_data)
            total_users = len(processed_data)
            optimal_browser_count = self.smart_browser_manager.calculate_optimal_browser_count(total_records, total_users)

            logger.info(f"🧠 智能决策: 使用 {optimal_browser_count} 个浏览器处理 {total_records} 条记录 ({total_users} 个用户)")

            # 🆕 第三步：根据浏览器数量选择处理策略
            if optimal_browser_count == 1:
                await self._process_data_with_single_browser(processed_data)
            else:
                await self._process_data_with_smart_parallel_batches(processed_data, optimal_browser_count)

            logger.info(f"✅ 据点 {self.facility_name} 数据处理完成")

        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 数据处理失败: {e}")
            raise

    async def _get_and_process_data(self):
        """🆕 获取和处理数据（不启动浏览器）"""
        try:
            # 获取数据
            sheets_client = SheetsClient()
            spreadsheet_id = self.facility_config.get('spreadsheet_id')
            sheet_name = self.facility_config.get('sheet_name', '看護記録')
            
            logger.info(f"📊 从据点 {self.facility_name} 获取数据: {spreadsheet_id} / {sheet_name}")
            
            # 使用正确的API方法读取数据
            range_name = f"{sheet_name}!A2:AK"  # 从第2行开始读取，避免表头
            raw_data = sheets_client.read_sheet(spreadsheet_id, range_name)

            # 处理数据
            from core.rpa_tools.tennki_data_processor import TennkiDataProcessor
            
            # 使用TennkiDataProcessor处理数据
            data_processor = TennkiDataProcessor(sheets_client, sheet_name)
            # 设置spreadsheet_id
            data_processor.sheets_client.spreadsheet_id = spreadsheet_id
            processed_data = await data_processor.preprocess_all_data()

            return processed_data

        except Exception as e:
            logger.error(f"❌ 数据获取和处理失败: {e}")
            raise

    async def _process_data_with_smart_parallel_batches(self, processed_data: List[Dict], optimal_browser_count: int):
        """🆕 智能并行批次处理（按利用者分割）"""
        try:
            from core.rpa_tools.tennki_data_splitter import TennkiDataSplitter, create_batch_info_list

            # 🆕 按利用者分割：每个用户一个批次
            logger.info(f"📦 按利用者分割数据: {len(processed_data)} 个用户")
            
            # 创建按用户分割的批次
            user_batches = []
            for i, user_data in enumerate(processed_data):
                batch = [user_data]
                user_batches.append(batch)
                logger.info(f"  批次 {i+1}: 用户 {user_data.get('user_name', 'unknown')} ({user_data['total_records']} 条记录)")

            # 🆕 确保批次数量不超过最优浏览器数量
            if len(user_batches) > optimal_browser_count:
                logger.warning(f"⚠️ 用户数({len(user_batches)})超过浏览器数({optimal_browser_count})，进行合并...")
                user_batches = self._merge_excess_batches(user_batches, optimal_browser_count)

            # 创建批次信息
            batch_info_list = create_batch_info_list(user_batches)

            # 🆕 使用增强批次处理器
            from core.rpa_tools.tennki_batch_processor import TennkiBatchProcessor
            
            batch_processor = TennkiBatchProcessor(
                self.facility_config,
                self.workflow_config,
                self.smart_browser_manager
            )

            # 🆕 设置动态并发数量
            batch_processor.max_concurrent_batches = optimal_browser_count
            batch_processor.semaphore = asyncio.Semaphore(optimal_browser_count)

            success = await batch_processor.process_batches_parallel(batch_info_list)

            # 合并失败数据
            batch_failed_collector = batch_processor.get_failed_data_collector()
            self.failed_data_collector.failed_records.extend(batch_failed_collector.failed_records)
            self.failed_data_collector.failed_users.extend(batch_failed_collector.failed_users)

            if not success:
                logger.warning("⚠️ 部分批次处理失败")

        except Exception as e:
            logger.error(f"❌ 智能并行批次处理失败: {e}", exc_info=True)
            raise

    def _merge_excess_batches(self, split_batches: List[List[Dict]], target_count: int) -> List[List[Dict]]:
        """🆕 合并多余的批次"""
        if len(split_batches) <= target_count:
            return split_batches

        logger.info(f"🔄 合并批次: {len(split_batches)} → {target_count}")

        # 简单合并策略：将最小的批次合并到其他批次中
        while len(split_batches) > target_count:
            # 找到最小的批次
            smallest_idx = min(range(len(split_batches)), 
                             key=lambda i: sum(user['total_records'] for user in split_batches[i]))
            
            # 找到第二小的批次进行合并
            remaining_batches = [i for i in range(len(split_batches)) if i != smallest_idx]
            target_idx = min(remaining_batches, 
                           key=lambda i: sum(user['total_records'] for user in split_batches[i]))

            # 合并批次
            split_batches[target_idx].extend(split_batches[smallest_idx])
            split_batches.pop(smallest_idx)

        logger.info(f"✅ 批次合并完成: {len(split_batches)} 个批次")
        return split_batches

    async def _process_data_with_single_browser(self, processed_data: List[Dict]):
        """单浏览器处理（在需要时启动浏览器）"""
        try:
            # 🆕 在需要时启动浏览器并登录
            await self.initialize_browser_and_login()

            # 🆕 修复：按照正确的流程顺序导航
            facility_name = self.facility_config.get('name')
            element_text = self.facility_config.get('element_text')

            # 1. 导航到据点
            await self.facility_manager.navigate_to_facility(element_text, facility_name)

            # 2. 导航到訪問看護页面（参考RPA代码流程）
            await self.facility_manager.navigate_to_nursing_page()

            # 3. 处理数据
            await self.form_engine.process_batch_data_sequential(processed_data, self.facility_config)

        except Exception as e:
            logger.error(f"❌ 单浏览器数据处理失败: {e}", exc_info=True)
            raise

    async def cleanup(self):
        """清理资源"""
        try:
            await self.smart_browser_manager.close_facility_browser(self.facility_name)
            logger.info(f"🔒 据点 {self.facility_name} 资源清理完成")
        except Exception as e:
            logger.error(f"❌ 据点 {self.facility_name} 资源清理失败: {e}")


class EnhancedTennkiFormEngine(TennkiFormEngine):
    """🆕 增强表单引擎 - 数据保护机制"""

    def __init__(self, selector_executor: SelectorExecutor, performance_monitor, failed_data_collector=None):
        super().__init__(selector_executor, performance_monitor, failed_data_collector)
        self.data_protection_enabled = True
        self.protected_field_values = {}

    async def _process_single_record(self, record: Dict, insurance_type: str):
        """处理单条记录（参考RPA代码实现）"""
        row_index = record['row_index']
        row = record.get('raw_data', [])
        
        try:
            logger.debug(f"📝 处理记录 (行 {row_index}) - 参考RPA代码实现")
            
            # 🆕 启用数据保护
            await self._enable_data_protection()
            
            # 🆕 根据参考文件实现保险种别处理
            if insurance_type == "介護":
                await self._process_kaigo_insurance_reference(row)
            elif insurance_type == "医療":
                await self._process_iryou_insurance_reference(row)
            elif insurance_type == "精神医療":
                await self._process_seishin_iryou_insurance_reference(row)
            else:
                logger.warning(f"⚠️ 未知保险类型: {insurance_type}")
                await super()._process_single_record(record, insurance_type)
            
            # 🆕 验证数据完整性
            await self._verify_data_integrity()
            
        except Exception as e:
            logger.error(f"❌ 记录 (行 {row_index}) 处理失败: {e}")
            raise
        finally:
            # 🆕 清理保护状态
            await self._cleanup_data_protection()

    async def _enable_data_protection(self):
        """🆕 启用数据保护机制"""
        if not self.data_protection_enabled:
            return
            
        try:
            page = self.selector_executor.page
            
            # 保存当前表单数据
            current_values = await page.evaluate("""
                () => {
                    const values = {};
                    const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    fields.forEach(field => {
                        if (field.value && field.value.trim() !== '') {
                            values[field.id || field.name || field.tagName + '_' + Array.from(field.parentNode.children).indexOf(field)] = field.value;
                        }
                    });
                    return values;
                }
            """)
            
            self.protected_field_values = current_values
            logger.debug(f"🛡️ 数据保护已启用，保护 {len(current_values)} 个字段")
            
        except Exception as e:
            logger.warning(f"⚠️ 启用数据保护失败: {e}")

    async def _verify_data_integrity(self):
        """🆕 验证数据完整性"""
        if not self.data_protection_enabled or not self.protected_field_values:
            return
            
        try:
            page = self.selector_executor.page
            
            # 检查字段是否被意外清空
            current_values = await page.evaluate("""
                () => {
                    const values = {};
                    const fields = document.querySelectorAll('#registModal input, #registModal select, #registModal textarea');
                    fields.forEach(field => {
                        values[field.id || field.name || field.tagName + '_' + Array.from(field.parentNode.children).indexOf(field)] = field.value || '';
                    });
                    return values;
                }
            """)
            
            # 检测被清空的字段
            cleared_fields = []
            for field_id, original_value in self.protected_field_values.items():
                current_value = current_values.get(field_id, '')
                if original_value and not current_value:
                    cleared_fields.append(field_id)
            
            if cleared_fields:
                logger.warning(f"⚠️ 检测到 {len(cleared_fields)} 个字段被意外清空: {cleared_fields}")
                
                # 尝试恢复被清空的字段
                for field_id in cleared_fields:
                    original_value = self.protected_field_values[field_id]
                    await page.evaluate(f"""
                        () => {{
                            const field = document.querySelector('[id="{field_id}"], [name="{field_id}"]');
                            if (field) {{
                                field.value = '{original_value}';
                                field.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                field.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                console.log('🛡️ 恢复字段值:', '{field_id}', '{original_value}');
                            }}
                        }}
                    """)
                
                logger.info(f"✅ 已尝试恢复 {len(cleared_fields)} 个被清空的字段")
            else:
                logger.debug("✅ 数据完整性验证通过，无字段被清空")
                
        except Exception as e:
            logger.warning(f"⚠️ 数据完整性验证失败: {e}")

    async def _cleanup_data_protection(self):
        """🆕 清理数据保护状态"""
        self.protected_field_values.clear()

    async def _process_kaigo_insurance_reference(self, row: List):
        """🆕 参考RPA代码处理介護保险（完全按照参考文件顺序）"""
        page = self.selector_executor.page
        
        try:
            logger.info("🏥 参考RPA代码处理介護保险")
            
            # 1. 点击介護保险按钮
            await page.click('#inPopupInsuranceDivision01')
            await page.wait_for_timeout(3000)
            
            # 2. 根据row[26]判断是否为介護予防
            if len(row) > 26 and row[26] == "":
                # 标准介護保险
                await page.select_option('#inPopupServiceKindId', value='4')  # 訪問看護
                await page.wait_for_timeout(200)
                await page.select_option('#inPopupEstimate1', label='通常の算定')
                await page.wait_for_timeout(200)
                
                # 3. 职员类型选择（参考文件中的顺序）
                if len(row) > 27:
                    staff_type = row[27]
                    if staff_type == "正看護師":
                        await page.select_option('#inPopupEstimate3', label='正看護師')
                    elif staff_type == "准看護師":
                        await page.select_option('#inPopupEstimate3', label='准看護師')
                    elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        await page.select_option('#inPopupEstimate3', label='作業療法士・理学療法士・言語聴覚士')
                
                # 4. 基本療養費（参考文件中的顺序）
                if len(row) > 17:
                    await page.select_option('#inPopupEstimate4', label=row[17])
                if len(row) > 18:
                    await page.select_option('#inPopupEstimate5', label=row[18])
                
                # 5. 同一日訪問人数（参考文件中的顺序）
                if len(row) > 34 and row[34] == "2":
                    await page.click('#inPopupserviceContentId1')
                    await page.wait_for_timeout(2000)
                
                # 6. 開始・終了時間（参考文件中的顺序）
                if len(row) > 8:
                    await page.select_option('#inPopupStartHour', label=row[8])
                if len(row) > 9:
                    await page.select_option('#inPopupStartMinute1', label=row[9])
                if len(row) > 10:
                    await page.select_option('#inPopupStartMinute2', label=row[10])
                if len(row) > 12:
                    await page.select_option('#inPopupEndHour', label=row[12])
                if len(row) > 13:
                    await page.select_option('#inPopupEndMinute1', label=row[13])
                if len(row) > 14:
                    await page.select_option('#inPopupEndMinute2', label=row[14])
                
            else:
                # 介護予防
                await page.select_option('#inPopupServiceKindId', value='18')  # 介護予防あり
                
                # 3. 职员类型选择（介護予防使用不同字段）
                if len(row) > 27:
                    staff_type = row[27]
                    if staff_type == "正看護師":
                        await page.select_option('#inPopupEstimate2', label='正看護師')
                    elif staff_type == "准看護師":
                        await page.select_option('#inPopupEstimate2', label='准看護師')
                    elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                        await page.select_option('#inPopupEstimate2', label='作業療法士・理学療法士・言語聴覚士')
                
                # 4. 基本療養費（介護予防使用不同字段）
                if len(row) > 17:
                    await page.select_option('#inPopupEstimate3', label=row[17])
                if len(row) > 18:
                    await page.select_option('#inPopupEstimate4', label=row[18])
                
                # 5. 同一日訪問人数（参考文件中的顺序）
                if len(row) > 34 and row[34] == "2":
                    await page.click('#inPopupserviceContentId1')
                    await page.wait_for_timeout(2000)
                
                # 6. 開始・終了時間（参考文件中的顺序）
                if len(row) > 8:
                    await page.select_option('#inPopupStartHour', label=row[8])
                if len(row) > 9:
                    await page.select_option('#inPopupStartMinute1', label=row[9])
                if len(row) > 10:
                    await page.select_option('#inPopupStartMinute2', label=row[10])
                if len(row) > 12:
                    await page.select_option('#inPopupEndHour', label=row[12])
                if len(row) > 13:
                    await page.select_option('#inPopupEndMinute1', label=row[13])
                if len(row) > 14:
                    await page.select_option('#inPopupEndMinute2', label=row[14])
            
            # 7. 予定・実績（参考文件中的顺序）
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(2000)
            if len(row) > 28:
                await page.click(row[28])
            await page.wait_for_timeout(2000)
            
            # 8. 職員情報
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)
            
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    await page.select_option('#chargeStaff1JobDivision1', label=row[27])
            
            # 9. 提交表单
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 介護保险处理完成（完全按照参考文件顺序）")
            
        except Exception as e:
            logger.error(f"❌ 介護保险处理失败: {e}")
            raise

    async def _process_iryou_insurance_reference(self, row: List):
        """🆕 参考RPA代码处理医療保险（修正表单填写顺序）"""
        page = self.selector_executor.page
        
        try:
            logger.info("🏥 参考RPA代码处理医療保险")
            
            # 1. 点击医療保险按钮
            await page.click('#inPopupInsuranceDivision02')
            await page.wait_for_timeout(2000)
            
            # 2. 选择服务类型和等级
            await page.select_option('#inPopupEstimate1', label='訪問看護')
            if len(row) > 32:
                await page.select_option('#inPopupEstimate2', label=row[32])
                await page.wait_for_timeout(200)
            
            # 🆕 3. 填写等级特定信息（在实绩选择之前）
            if len(row) > 32 and row[32] == "Ⅱ":
                if len(row) > 33:
                    await page.select_option('#inPopupEstimate4', label=row[33])
                    await page.wait_for_timeout(200)
            
            # 🆕 4. 填写開始・終了時間（在实绩选择之前）
            if len(row) > 8:
                await page.select_option('#inPopupStartHour', label=row[8])
            if len(row) > 9:
                await page.select_option('#inPopupStartMinute1', label=row[9])
            if len(row) > 10:
                await page.select_option('#inPopupStartMinute2', label=row[10])
            if len(row) > 12:
                await page.select_option('#inPopupEndHour', label=row[12])
            if len(row) > 13:
                await page.select_option('#inPopupEndMinute1', label=row[13])
            if len(row) > 14:
                await page.select_option('#inPopupEndMinute2', label=row[14])
            
            # 5. 职员类型选择
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#inPopupEstimate3', label='看護師等')
                elif staff_type == "准看護師":
                    await page.select_option('#inPopupEstimate3', label='准看護師')
                elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    await page.select_option('#inPopupEstimate3', label='理学療法士等')
            
            # 🆕 6. 实绩选择（在所有其他字段填写完成后）
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(2000)
            if len(row) > 28:
                await page.click(row[28])
            await page.wait_for_timeout(2000)
            
            # 7. 职员信息
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)
            
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    await page.select_option('#chargeStaff1JobDivision1', label=row[27])
            
            # 8. 提交表单
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 医療保险处理完成（参考RPA代码）")
            
        except Exception as e:
            logger.error(f"❌ 医療保险处理失败: {e}")
            raise

    async def _process_seishin_iryou_insurance_reference(self, row: List):
        """🆕 参考RPA代码处理精神医療保险"""
        page = self.selector_executor.page
        
        try:
            logger.info("🏥 参考RPA代码处理精神医療保险")
            
            # 1. 点击医療保险按钮
            await page.click('#inPopupInsuranceDivision02')
            await page.wait_for_timeout(3000)
            
            # 2. 选择服务类型和等级
            await page.select_option('#inPopupEstimate1', label='精神科訪問看護')
            if len(row) > 32:
                await page.select_option('#inPopupEstimate2', label=row[32])
            
            # 3. 职员类型选择
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#inPopupEstimate3', label='看護師等')
                elif staff_type == "准看護師":
                    await page.select_option('#inPopupEstimate3', label='准看護師')
                elif staff_type in ["理学療法士", "言語聴覚士", "作業療法士"]:
                    await page.select_option('#inPopupEstimate3', label='作業療法士')
            
            # 4. 等级特定信息
            if len(row) > 32 and row[32] == "Ⅲ":
                if len(row) > 33:
                    await page.select_option('#inPopupEstimate4', label=row[33])
            
            # 5. 时间信息填写
            if len(row) > 8:
                await page.select_option('#inPopupStartHour', label=row[8])
            if len(row) > 9:
                await page.select_option('#inPopupStartMinute1', label=row[9])
            if len(row) > 10:
                await page.select_option('#inPopupStartMinute2', label=row[10])
            if len(row) > 12:
                await page.select_option('#inPopupEndHour', label=row[12])
            if len(row) > 13:
                await page.select_option('#inPopupEndMinute1', label=row[13])
            if len(row) > 14:
                await page.select_option('#inPopupEndMinute2', label=row[14])
            
            # 6. 实绩选择
            await page.click('#inPopupPlanAchievementsDivision02')
            await page.wait_for_timeout(1000)
            if len(row) > 28:
                await page.click(row[28])
            await page.wait_for_timeout(2000)
            
            # 7. 职员信息
            await page.click('#input_staff_on .btn')
            await page.wait_for_timeout(2000)
            
            if len(row) > 27:
                staff_type = row[27]
                if staff_type == "正看護師":
                    await page.select_option('#chargeStaff1JobDivision1', label='看護師')
                else:
                    await page.select_option('#chargeStaff1JobDivision1', label=row[27])
            
            # 8. 提交表单
            await page.click('#btnRegisPop')
            await page.wait_for_timeout(2000)
            
            logger.info("✅ 精神医療保险处理完成（参考RPA代码）")
            
        except Exception as e:
            logger.error(f"❌ 精神医療保险处理失败: {e}")
            raise

    async def _create_selective_event_interceptor(self, page, mode="time"):
        """🆕 增强事件拦截器 - 避免过度拦截"""
        try:
            logger.debug(f"🛡️ 创建增强事件拦截器 (模式: {mode})")
            
            # 🆕 更精确的事件拦截，避免影响必要的表单验证
            await page.evaluate(f"""
                () => {{
                    const mode = '{mode}';
                    
                    // 🆕 保护关键表单事件，但允许必要的验证
                    const protectedEvents = ['beforeunload', 'unload'];
                    const allowedValidationEvents = ['blur', 'focus', 'input', 'change'];
                    
                    // 只拦截可能导致页面跳转或数据丢失的事件
                    protectedEvents.forEach(eventType => {{
                        window.addEventListener(eventType, function(e) {{
                            const registModal = document.querySelector('#registModal');
                            if (registModal && registModal.style.display !== 'none') {{
                                console.log('🛡️ 拦截可能导致数据丢失的事件:', eventType);
                                e.preventDefault();
                                e.stopPropagation();
                                return false;
                            }}
                        }}, true);
                    }});
                    
                    // 🆕 智能拦截：只在特定条件下拦截时间相关函数
                    if (mode === 'time') {{
                        const originalPopulateEndTime = window.populateEstimationEndTime;
                        window.populateEstimationEndTime = function() {{
                            const registModal = document.querySelector('#registModal');
                            const hasUserInput = document.querySelector('#estimationEndTime')?.value;
                            
                            // 只有在用户已输入数据时才拦截自动填充
                            if (registModal && registModal.style.display !== 'none' && hasUserInput) {{
                                console.log('🛡️ 拦截自动时间填充，保护用户输入');
                                return;
                            }}
                            
                            // 否则允许正常执行
                            if (originalPopulateEndTime) {{
                                return originalPopulateEndTime.apply(this, arguments);
                            }}
                        }};
                    }}
                    
                    console.log('🛡️ 增强事件拦截器已激活 (模式: ' + mode + ')');
                }}
            """)
            
        except Exception as e:
            logger.warning(f"⚠️ 创建增强事件拦截器失败: {e}")


class TennkiWorkflowManager:
    """🆕 增强工作流管理器 - 智能浏览器管理"""

    def __init__(self, workflow_config: dict):
        self.workflow_config = workflow_config
        self.smart_browser_manager = SmartBrowserManager()
        self.facility_processors = {}
        self.global_failed_data_collector = TennkiFailedDataCollector()

    async def run_workflow(self):
        """运行增强工作流"""
        logger.info("🚀 启动 Kaipoke Tennki 增强工作流")
        
        try:
            # 初始化智能浏览器管理器
            await self.smart_browser_manager.initialize()

            # 获取据点配置
            facility_configs = self.workflow_config.get('facilities', [])
            
            if not facility_configs:
                logger.warning("⚠️ 没有配置据点信息")
                return False

            # 处理每个据点
            for facility_config in facility_configs:
                try:
                    processor = EnhancedTennkiFacilityProcessor(
                        facility_config, 
                        self.workflow_config, 
                        self.smart_browser_manager
                    )
                    
                    await self._process_facility_with_error_handling(processor)
                    
                except Exception as e:
                    logger.error(f"❌ 据点处理失败: {e}")

            # 收集所有失败数据
            await self._collect_all_failed_data()

            # 导出失败数据
            await self._export_failed_data()

            return True

        except Exception as e:
            logger.error(f"❌ 工作流执行失败: {e}")
            return False
        finally:
            # 清理所有资源
            await self._cleanup_all_resources()

    async def _process_facility_with_error_handling(self, processor: EnhancedTennkiFacilityProcessor):
        """带错误处理的据点处理"""
        facility_name = processor.facility_name
        try:
            # 🆕 第一步：初始化处理器（不启动浏览器）
            await processor.initialize()
            
            # 🆕 第二步：处理据点数据（会根据数据量决定是否启动浏览器）
            await processor.process_facility_data()
            
            logger.info(f"✅ 据点 {facility_name} 处理成功")
        except Exception as e:
            logger.error(f"❌ 据点 {facility_name} 处理异常: {e}", exc_info=True)
            return False
        finally:
            try:
                await processor.cleanup()
            except Exception as e:
                logger.warning(f"⚠️ 据点 {facility_name} 清理时出错: {e}")

    async def _collect_all_failed_data(self):
        """收集所有据点的失败数据"""
        for processor in self.facility_processors.values():
            if hasattr(processor, 'failed_data_collector'):
                collector = processor.failed_data_collector
                self.global_failed_data_collector.failed_records.extend(collector.failed_records)
                self.global_failed_data_collector.failed_users.extend(collector.failed_users)

    async def _export_failed_data(self):
        """🆕 导出失败数据（增强详细输出）"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            failed_data_file = f"failed_data_tennki_{timestamp}.json"
            
            # 🆕 增强失败数据收集
            failed_data = {
                'timestamp': timestamp,
                'failed_records': self.global_failed_data_collector.failed_records,
                'failed_users': self.global_failed_data_collector.failed_users,
                'summary': {
                    'total_failed_records': len(self.global_failed_data_collector.failed_records),
                    'total_failed_users': len(self.global_failed_data_collector.failed_users)
                }
            }
            
            import json
            with open(failed_data_file, 'w', encoding='utf-8') as f:
                json.dump(failed_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📄 失败数据已导出到: {failed_data_file}")
            
            # 🆕 增强失败数据日志输出
            self._print_detailed_failed_data_log()
            
        except Exception as e:
            logger.error(f"❌ 导出失败数据时出错: {e}")

    def _print_detailed_failed_data_log(self):
        """🆕 打印详细的失败数据日志（便于手动登录）"""
        logger.info("=" * 80)
        logger.info("📋 失败数据详细报告（便于手动登录）")
        logger.info("=" * 80)

        # 用户级别失败
        if self.global_failed_data_collector.failed_users:
            logger.info(f"\n👥 失败用户列表 ({len(self.global_failed_data_collector.failed_users)} 个):")
            for i, user in enumerate(self.global_failed_data_collector.failed_users, 1):
                logger.info(f"  {i}. 据点: {user['facility_name']}")
                logger.info(f"     用户: {user['user_name']}")
                logger.info(f"     错误: {user['error_message']}")
                logger.info(f"     时间: {user['timestamp']}")
                logger.info("")

        # 记录级别失败
        if self.global_failed_data_collector.failed_records:
            logger.info(f"\n📝 失败记录列表 ({len(self.global_failed_data_collector.failed_records)} 条):")
            for i, record in enumerate(self.global_failed_data_collector.failed_records, 1):
                logger.info(f"  {i}. 据点: {record['facility_name']}")
                logger.info(f"     用户: {record['user_name']}")
                logger.info(f"     行号: {record['row_index']}")
                logger.info(f"     保险: {record['insurance_type']}")
                logger.info(f"     实施日: {record['service_date']}")
                logger.info(f"     开始时间: {record['start_time']}")
                logger.info(f"     结束时间: {record['end_time']}")
                logger.info(f"     错误: {record['error_message']}")
                logger.info(f"     时间: {record['timestamp']}")
                logger.info(f"     原始数据: {record.get('raw_data', [])}")
                logger.info("")

        # 按据点统计
        facility_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            facility = record['facility_name']
            if facility not in facility_stats:
                facility_stats[facility] = 0
            facility_stats[facility] += 1

        for user in self.global_failed_data_collector.failed_users:
            facility = user['facility_name']
            if facility not in facility_stats:
                facility_stats[facility] = 0
            facility_stats[facility] += 1

        if facility_stats:
            logger.info("📊 按据点失败统计:")
            for facility, count in facility_stats.items():
                logger.info(f"  - {facility}: {count} 条失败")

        # 🆕 增强：按保险类型统计
        insurance_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            insurance = record.get('insurance_type', 'unknown')
            if insurance not in insurance_stats:
                insurance_stats[insurance] = 0
            insurance_stats[insurance] += 1

        if insurance_stats:
            logger.info("🏥 按保险类型失败统计:")
            for insurance, count in insurance_stats.items():
                logger.info(f"  - {insurance}: {count} 条失败")

        # 🆕 增强：按错误类型统计
        error_stats = {}
        for record in self.global_failed_data_collector.failed_records:
            error_msg = record.get('error_message', 'unknown')
            # 提取错误类型（前50个字符）
            error_type = error_msg[:50] + "..." if len(error_msg) > 50 else error_msg
            if error_type not in error_stats:
                error_stats[error_type] = 0
            error_stats[error_type] += 1

        if error_stats:
            logger.info("❌ 按错误类型统计:")
            for error_type, count in error_stats.items():
                logger.info(f"  - {error_type}: {count} 条失败")

        logger.info("=" * 80)

    async def _cleanup_all_resources(self):
        """清理所有资源"""
        try:
            await self.smart_browser_manager.close_all_browsers()
            logger.info("🔒 所有资源清理完成")
        except Exception as e:
            logger.warning(f"⚠️ 资源清理时出错: {e}")


from dotenv import load_dotenv

def run(config: dict):
    """工作流入口函数（同步版本，供main.py调用）"""
    
    async def async_run():
        """异步执行函数"""
        # 环境变量加载
        load_dotenv()
        
        # 使用传入的config参数
        current_config = config
        logger.debug(f"CONFIG: {current_config}")
        
        # 确保 facilities 列表存在
        facilities = current_config.get('facilities') or current_config.get('config', {}).get('facilities')
        if not facilities:
            logger.error("❌ 工作流配置中缺少 'facilities' 部分。")
            return False
        
        # 更新配置结构以确保兼容性
        if 'config' in current_config:
            # 如果配置是嵌套结构，提取内部配置
            current_config = current_config['config']

        # 验证每个facility都有必要的配置
        for i, facility in enumerate(current_config['facilities']):
            if 'spreadsheet_id' not in facility or not facility['spreadsheet_id']:
                logger.error(f"❌ 据点 {i+1} 缺少 spreadsheet_id 配置")
                return False
            logger.info(f"✅ 据点配置验证: {facility['name']} -> {facility['spreadsheet_id']}")

        # 创建并运行工作流管理器
        workflow_manager = TennkiWorkflowManager(current_config)
        success = await workflow_manager.run_workflow()
        
        if success:
            logger.info("🎉 所有据点处理成功！")
        else:
            logger.warning("⚠️ 部分据点处理失败，请检查日志")
        
        return success

    asyncio.run(async_run())


if __name__ == "__main__":
    # 直接运行时，需要一个模拟的 config
    # 这部分主要用于直接测试此脚本
    from configs.workflows import WORKFLOWS
    
    # 假设我们要测试 'kaipoke_tennki_refactored'
    test_workflow_id = 'kaipoke_tennki_refactored'
    if test_workflow_id in WORKFLOWS:
        test_config = WORKFLOWS[test_workflow_id]
        run(test_config)
    else:
        logger.error(f"测试工作流ID '{test_workflow_id}' 在 workflows.yaml 中未找到。")
