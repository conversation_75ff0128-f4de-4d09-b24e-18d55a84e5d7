"""
Kaipoke专用弹窗处理器
为所有kaipoke工作流提供统一的登录后弹窗处理机制
"""
import asyncio
from typing import List, Dict, Optional
from logger_config import logger


class KaipokePopupHandler:
    """
    Kaipoke专用弹窗处理器
    
    功能：
    1. 处理登录后出现的各种弹窗（Karte组件、通知弹窗等）
    2. 保护特定工作流的数据表单不被误关闭
    3. 提供模块化的弹窗检测和关闭机制
    """
    
    def __init__(self):
        self.popup_rules = self._initialize_popup_rules()
        self.protected_contexts = ['tennki_form', 'data_entry', 'modal_form']
    
    def _initialize_popup_rules(self) -> List[Dict]:
        """初始化弹窗处理规则"""
        return [
            {
                'name': 'aria_label_close_button',
                'description': 'aria-label关闭按钮（最高稳定性）',
                'selectors': [],  # 使用特殊的aria-label处理
                'aria_labels': ['閉じる', 'Close', '关闭', '×'],
                'priority': 110,  # 最高优先级
                'action': 'aria_click',
                'wait_after': 1000,
                'context_safe': True  # 对所有上下文都安全
            },
            {
                'name': 'karte_widget_popup',
                'description': 'Karte组件弹窗（新发现）',
                'selectors': [
                    '#karte-2458602 > div.karte-widget__container > div > div > section > div > div > button',
                    '#karte-2458602 button[class*="close"]',
                    '#karte-2458602 .karte-widget__container button',
                    '[id^="karte-"] button[class*="close"]',
                    '[id^="karte-"] .karte-widget__container button'
                ],
                'priority': 100,
                'action': 'click',
                'wait_after': 1000,
                'context_safe': True  # 对所有上下文都安全
            },
            {
                'name': 'karte_component_general',
                'description': 'Karte组件通用处理',
                'selectors': [
                    '[id^="karte-"]',
                    '.karte-widget',
                    '.karte-widget__container'
                ],
                'priority': 90,
                'action': 'remove',
                'wait_after': 500,
                'context_safe': True
            },
            {
                'name': 'notification_popup',
                'description': '通知弹窗（仅限非保护上下文）',
                'selectors': [
                    '._icon-close__bF1y_',
                    '.notification-close',
                    '.alert-close'
                ],
                'priority': 80,
                'action': 'click',
                'wait_after': 500,
                'context_safe': False,  # 需要检查上下文
                'protected_selectors': [
                    '#registModal',
                    '.modal-dialog',
                    '.data-entry-form'
                ]
            },
            {
                'name': 'general_modal_overlay',
                'description': '通用模态遮罩（仅限非保护上下文）',
                'selectors': [
                    '.modal-backdrop',
                    '.overlay',
                    '.popup-overlay'
                ],
                'priority': 70,
                'action': 'remove',
                'wait_after': 300,
                'context_safe': False
            }
        ]
    
    async def handle_login_popups(self, page, context: str = "general") -> bool:
        """
        处理登录后弹窗
        
        Args:
            page: Playwright页面对象
            context: 上下文标识（用于保护特定表单）
            
        Returns:
            bool: 是否处理了弹窗
        """
        try:
            logger.debug(f"🔍 开始处理Kaipoke登录后弹窗 (上下文: {context})")
            
            # 检查是否为保护上下文
            is_protected_context = any(protected in context.lower() 
                                     for protected in self.protected_contexts)
            
            if is_protected_context:
                logger.debug(f"🛡️ 检测到保护上下文: {context}，仅处理安全弹窗")
            
            handled_count = 0
            
            # 按优先级处理弹窗
            for rule in sorted(self.popup_rules, key=lambda x: x['priority'], reverse=True):
                if await self._handle_popup_by_rule(page, rule, context, is_protected_context):
                    handled_count += 1
                    logger.info(f"✅ 成功处理弹窗: {rule['name']}")
                    
                    # 等待处理完成
                    await page.wait_for_timeout(rule.get('wait_after', 500))
            
            if handled_count > 0:
                logger.info(f"✅ 共处理了 {handled_count} 个弹窗")
                return True
            else:
                logger.debug("ℹ️ 未发现需要处理的弹窗")
                return False
                
        except Exception as e:
            logger.error(f"❌ 处理登录后弹窗失败: {e}")
            return False
    
    async def _handle_popup_by_rule(self, page, rule: Dict, context: str,
                                   is_protected_context: bool) -> bool:
        """根据规则处理单个弹窗"""
        try:
            # 检查上下文安全性
            if is_protected_context and not rule.get('context_safe', False):
                logger.debug(f"🛡️ 跳过非安全弹窗规则: {rule['name']} (保护上下文)")
                return False

            # 🆕 特殊处理aria-label规则
            if rule.get('action') == 'aria_click':
                return await self._handle_aria_label_popup(page, rule)

            # 检查弹窗是否存在
            popup_found = False
            target_selector = None

            for selector in rule['selectors']:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        element = page.locator(selector).first
                        if await element.is_visible():
                            popup_found = True
                            target_selector = selector
                            logger.debug(f"🔍 发现弹窗: {rule['name']} - {selector}")
                            break
                except Exception as e:
                    logger.debug(f"检查选择器失败 {selector}: {e}")
                    continue

            if not popup_found:
                return False
            
            # 保护检查：确保不会误关闭保护的元素
            if not rule.get('context_safe', False):
                protected_selectors = rule.get('protected_selectors', [])
                for protected_selector in protected_selectors:
                    try:
                        if await page.locator(protected_selector).count() > 0:
                            logger.debug(f"🛡️ 检测到保护元素，跳过处理: {protected_selector}")
                            return False
                    except:
                        continue
            
            # 执行处理动作
            action = rule.get('action', 'click')
            success = await self._execute_popup_action(page, target_selector, action)
            
            if success:
                logger.debug(f"✅ 成功执行动作 {action} 于选择器: {target_selector}")
                return True
            
            return False

        except Exception as e:
            logger.debug(f"处理弹窗规则失败 {rule['name']}: {e}")
            return False

    async def _handle_aria_label_popup(self, page, rule: Dict) -> bool:
        """处理基于aria-label的弹窗"""
        try:
            aria_labels = rule.get('aria_labels', [])

            for aria_label in aria_labels:
                try:
                    # 使用Playwright的get_by_label方法
                    element = page.get_by_label(aria_label)
                    count = await element.count()

                    if count > 0:
                        # 检查元素是否可见
                        first_element = element.first
                        if await first_element.is_visible():
                            await first_element.click(timeout=5000)
                            logger.info(f"✅ 成功通过aria-label关闭弹窗: {aria_label}")
                            return True

                except Exception as e:
                    logger.debug(f"aria-label处理失败 {aria_label}: {e}")
                    continue

            return False

        except Exception as e:
            logger.debug(f"aria-label弹窗处理异常: {e}")
            return False

    async def _execute_popup_action(self, page, selector: str, action: str) -> bool:
        """执行弹窗处理动作"""
        try:
            if action == 'click':
                await page.locator(selector).first.click(timeout=5000)
                logger.debug(f"✅ 点击元素: {selector}")
                return True
            elif action == 'remove':
                await page.evaluate(f"""
                    () => {{
                        const elements = document.querySelectorAll('{selector}');
                        elements.forEach(el => el.remove());
                        return elements.length > 0;
                    }}
                """)
                logger.debug(f"✅ 移除元素: {selector}")
                return True
            elif action == 'hide':
                await page.evaluate(f"""
                    () => {{
                        const elements = document.querySelectorAll('{selector}');
                        elements.forEach(el => el.style.display = 'none');
                        return elements.length > 0;
                    }}
                """)
                logger.debug(f"✅ 隐藏元素: {selector}")
                return True
            else:
                logger.warning(f"⚠️ 未知动作: {action}")
                return False
                
        except Exception as e:
            logger.debug(f"执行动作失败 {action} - {selector}: {e}")
            return False
    
    async def handle_specific_karte_popup(self, page) -> bool:
        """
        专门处理新发现的Karte弹窗
        
        Args:
            page: Playwright页面对象
            
        Returns:
            bool: 是否成功处理
        """
        try:
            logger.debug("🎯 专门处理Karte弹窗...")
            
            # 新发现的Karte弹窗选择器
            karte_selectors = [
                '#karte-2458602 > div.karte-widget__container > div > div > section > div > div > button',
                '#karte-2458602 button',
                '[id^="karte-"] button'
            ]
            
            for selector in karte_selectors:
                try:
                    count = await page.locator(selector).count()
                    if count > 0:
                        element = page.locator(selector).first
                        if await element.is_visible():
                            await element.click(timeout=5000)
                            logger.info(f"✅ 成功关闭Karte弹窗: {selector}")
                            await page.wait_for_timeout(1000)
                            return True
                except Exception as e:
                    logger.debug(f"Karte弹窗处理失败 {selector}: {e}")
                    continue
            
            logger.debug("ℹ️ 未发现Karte弹窗")
            return False
            
        except Exception as e:
            logger.error(f"❌ Karte弹窗专门处理失败: {e}")
            return False


# 全局实例
kaipoke_popup_handler = KaipokePopupHandler()


# 便捷函数
async def handle_kaipoke_login_popups(page, context: str = "general") -> bool:
    """
    便捷函数：处理Kaipoke登录后弹窗
    
    使用示例:
    ```python
    from core.popup_handler.kaipoke_popup_handler import handle_kaipoke_login_popups
    
    # 通用处理
    await handle_kaipoke_login_popups(page)
    
    # 保护上下文处理
    await handle_kaipoke_login_popups(page, "tennki_form")
    ```
    """
    return await kaipoke_popup_handler.handle_login_popups(page, context)


async def handle_karte_popup_only(page) -> bool:
    """
    便捷函数：仅处理Karte弹窗
    """
    return await kaipoke_popup_handler.handle_specific_karte_popup(page)
