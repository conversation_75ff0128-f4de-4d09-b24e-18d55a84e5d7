from typing import Optional
from logger_config import logger

class TennkiFacilityManager:
    """
    Tennki据点页面管理器（修复版 - 参考RPA代码实现）
    - 负责导航到指定据点、切换到看护页面等
    - 依赖 selector_executor 进行页面操作
    - 修复据点导航超时问题
    """

    def __init__(self, selector_executor):
        self.selector_executor = selector_executor

    async def navigate_to_facility(self, element_text: str, facility_name: Optional[str] = None):
        """
        导航到指定据点（重构版 - 多重策略fallback机制）
        """
        page = self.selector_executor.page
        logger.info(f"🏥 开始导航到据点: {element_text} ({facility_name})")
        
        try:
            # 第一步：等待页面完全加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            logger.debug("✅ 页面加载完成")
            
            # 第二步：多重策略查找据点链接
            facility_found = False
            
            # 策略1：使用智能选择器
            try:
                success = await self.selector_executor.smart_click(
                    workflow="kaipoke_tennki",
                    category="navigation",
                    element="facility_selection",
                    target_text=element_text
                )
                if success:
                    facility_found = True
                    logger.info(f"✅ 智能选择器成功导航到据点: {element_text}")
            except Exception as e:
                logger.debug(f"⚠️ 智能选择器失败: {e}")
            
            # 策略2：直接文本匹配
            if not facility_found:
                try:
                    await page.wait_for_selector(f"text={element_text}", timeout=10000)
                    await page.click(f"text={element_text}")
                    facility_found = True
                    logger.info(f"✅ 文本匹配成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ 文本匹配失败: {e}")
            
            # 策略3：XPath查找
            if not facility_found:
                try:
                    xpath_selector = f"//a[contains(text(), '{element_text}')]"
                    await page.wait_for_selector(f"xpath={xpath_selector}", timeout=10000)
                    await page.click(f"xpath={xpath_selector}")
                    facility_found = True
                    logger.info(f"✅ XPath成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ XPath查找失败: {e}")
            
            # 策略4：部分文本匹配
            if not facility_found:
                try:
                    # 提取据点ID部分进行匹配
                    if "/" in element_text:
                        facility_id = element_text.split("/")[-1]
                        partial_selector = f"a:has-text('{facility_id}')"
                        await page.wait_for_selector(partial_selector, timeout=10000)
                        await page.click(partial_selector)
                        facility_found = True
                        logger.info(f"✅ 部分匹配成功导航到据点: {facility_id}")
                except Exception as e:
                    logger.debug(f"⚠️ 部分匹配失败: {e}")
            
            # 策略5：JavaScript查找
            if not facility_found:
                try:
                    js_result = await page.evaluate(f"""
                        () => {{
                            const links = Array.from(document.querySelectorAll('a'));
                            const targetLink = links.find(link =>
                                link.textContent.includes('{element_text}') ||
                                link.href.includes('{element_text}') ||
                                link.getAttribute('href')?.includes('{element_text}')
                            );
                            if (targetLink) {{
                                targetLink.click();
                                return true;
                            }}
                            return false;
                        }}
                    """)
                    if js_result:
                        facility_found = True
                        logger.info(f"✅ JavaScript查找成功导航到据点: {element_text}")
                except Exception as e:
                    logger.debug(f"⚠️ JavaScript查找失败: {e}")
            
            if not facility_found:
                # 诊断信息：列出页面上所有可用的链接
                await self._diagnose_available_facilities(page)
                raise RuntimeError(f"所有策略都无法找到据点: {element_text}")
            
            # 第三步：等待据点页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            logger.info(f"✅ 成功导航到据点: {element_text} ({facility_name})")
            
        except Exception as e:
            logger.error(f"❌ 导航到据点失败: {element_text} ({facility_name}): {e}")
            raise RuntimeError(f"导航到据点失败: {element_text} ({facility_name}): {e}")

    async def _diagnose_available_facilities(self, page):
        """🆕 诊断页面上可用的据点链接"""
        try:
            logger.info("🔍 诊断页面上可用的据点链接...")
            
            available_links = await page.evaluate("""
                () => {
                    const links = Array.from(document.querySelectorAll('a'));
                    return links
                        .filter(link => link.textContent.trim() && link.href)
                        .map(link => ({
                            text: link.textContent.trim(),
                            href: link.href,
                            visible: link.offsetParent !== null
                        }))
                        .slice(0, 20); // 只显示前20个链接
                }
            """)
            
            logger.info("📋 页面上可用的链接:")
            for i, link in enumerate(available_links, 1):
                visibility = "可见" if link['visible'] else "隐藏"
                logger.info(f"  {i}. {link['text']} ({visibility})")
                logger.debug(f"     URL: {link['href']}")
                
        except Exception as e:
            logger.warning(f"⚠️ 诊断据点链接失败: {e}")

    async def navigate_to_nursing_page(self):
        """
        导航到看护页面（修复版 - 参考RPA代码实现）
        """
        page = self.selector_executor.page
        logger.info("🏥 开始导航到看护页面")
        
        try:
            # 🆕 多重策略查找看护记录页面
            nursing_found = False
            
            # 策略1：使用智能选择器
            try:
                success = await self.selector_executor.smart_click(
                    workflow="kaipoke_tennki",
                    category="navigation",
                    element="nursing_menu_hover"
                )
                if success:
                    # 等待下拉菜单出现
                    await page.wait_for_timeout(1000)
                    
                    # 点击看护记录菜单项
                    success2 = await self.selector_executor.smart_click(
                        workflow="kaipoke_tennki",
                        category="navigation",
                        element="nursing_menu_click"
                    )
                    if success2:
                        nursing_found = True
                        logger.info("✅ 智能选择器成功导航到看护页面")
            except Exception as e:
                logger.debug(f"⚠️ 智能选择器失败: {e}")
            
            # 策略2：直接文本匹配
            if not nursing_found:
                try:
                    await page.wait_for_selector("text=看護記録", timeout=10000)
                    await page.click("text=看護記録")
                    nursing_found = True
                    logger.info("✅ 文本匹配成功导航到看护页面")
                except Exception as e:
                    logger.debug(f"⚠️ 文本匹配失败: {e}")
            
            # 策略3：查找包含"看護"的链接
            if not nursing_found:
                try:
                    nursing_links = await page.evaluate("""
                        () => {
                            const links = Array.from(document.querySelectorAll('a'));
                            return links.filter(link =>
                                link.textContent.includes('看護') ||
                                link.textContent.includes('記録') ||
                                link.href.includes('nursing') ||
                                link.href.includes('record')
                            );
                        }
                    """)
                    
                    if nursing_links:
                        # 点击第一个匹配的链接
                        await page.click("a:has-text('看護')")
                        nursing_found = True
                        logger.info("✅ 看护链接匹配成功导航到看护页面")
                except Exception as e:
                    logger.debug(f"⚠️ 看护链接匹配失败: {e}")
            
            if not nursing_found:
                # 🆕 诊断信息：列出页面上所有可用的菜单
                await self._diagnose_available_menus(page)
                raise RuntimeError("所有策略都无法找到看护记录页面")
            
            # 🆕 等待看护页面加载
            await page.wait_for_load_state('networkidle', timeout=30000)
            logger.info("✅ 成功导航到看护页面")
            
        except Exception as e:
            logger.error(f"❌ 导航到看护页面失败: {e}")
            raise RuntimeError(f"导航到看护页面失败: {e}")

    async def _diagnose_available_menus(self, page):
        """🆕 诊断页面上可用的菜单项"""
        try:
            logger.info("🔍 诊断页面上可用的菜单项...")
            
            available_menus = await page.evaluate("""
                () => {
                    const menus = Array.from(document.querySelectorAll('a, button, .menu-item, .dropdown-item'));
                    return menus
                        .filter(menu => menu.textContent.trim())
                        .map(menu => ({
                            text: menu.textContent.trim(),
                            tag: menu.tagName,
                            classes: menu.className,
                            visible: menu.offsetParent !== null
                        }))
                        .slice(0, 15); // 只显示前15个菜单项
                }
            """)
            
            logger.info("📋 页面上可用的菜单项:")
            for i, menu in enumerate(available_menus, 1):
                visibility = "可见" if menu['visible'] else "隐藏"
                logger.info(f"  {i}. {menu['text']} ({menu['tag']}, {visibility})")
                
        except Exception as e:
            logger.warning(f"⚠️ 诊断菜单项失败: {e}")