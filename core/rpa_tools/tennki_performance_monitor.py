import time

class TennkiPerformanceMonitor:
    """
    Tennki性能监控器
    - 记录处理开始/结束时间
    - 统计用时、输出日志
    """

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.records_processed = 0

    def start(self):
        self.start_time = time.time()

    def end(self):
        self.end_time = time.time()

    def log_record_processed(self):
        self.records_processed += 1

    def get_summary(self):
        duration = (self.end_time - self.start_time) if self.start_time and self.end_time else None
        return {
            "records_processed": self.records_processed,
            "duration_seconds": duration
        }